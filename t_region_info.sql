CREATE TABLE "public"."t_region_info" (
"id" int4 NOT NULL GENERATED ALWAYS AS IDENTITY (
INCREMENT 1
MINVALUE  1
MAXVALUE 2147483647
START 1
CACHE 1
),
"code" varchar(255) COLLATE "pg_catalog"."default",
"name" varchar(255) COLLATE "pg_catalog"."default",
"sheng" varchar(255) COLLATE "pg_catalog"."default",
"shi" varchar(255) COLLATE "pg_catalog"."default",
"qu" varchar(255) COLLATE "pg_catalog"."default",
"zhen" varchar(255) COLLATE "pg_catalog"."default",
"zuoluo" varchar(255) COLLATE "pg_catalog"."default",
"center" geometry(POINT),
"region" geometry(MULTIPOLYGON),
"organization_id" int4,
"remark" varchar(255) COLLATE "pg_catalog"."default",
"create_dt" timestamp(0),
"create_by" int4,
"update_dt" timestamp(0),
"update_by" int4,
"status" int2,
"river" varchar(255) COLLATE "pg_catalog"."default",
"sample_dt" timestamp(6),
"number" varchar(255) COLLATE "pg_catalog"."default",
"method" varchar(255) COLLATE "pg_catalog"."default",
"unit" varchar(255) COLLATE "pg_catalog"."default",
"administrative_code" varchar(50) COLLATE "pg_catalog"."default",
CONSTRAINT "t_region_info_pkey" PRIMARY KEY ("id")
);
ALTER TABLE "public"."t_region_info"
    OWNER TO "postgres";
COMMENT ON COLUMN "public"."t_region_info"."id" IS '地物ID';
COMMENT ON COLUMN "public"."t_region_info"."code" IS '地物编码';
COMMENT ON COLUMN "public"."t_region_info"."name" IS '地物名称';
COMMENT ON COLUMN "public"."t_region_info"."sheng" IS '省名称';
COMMENT ON COLUMN "public"."t_region_info"."shi" IS '市名称';
COMMENT ON COLUMN "public"."t_region_info"."qu" IS '区县名称';
COMMENT ON COLUMN "public"."t_region_info"."zhen" IS '街镇名称';
COMMENT ON COLUMN "public"."t_region_info"."zuoluo" IS '坐落位置';
COMMENT ON COLUMN "public"."t_region_info"."center" IS '中心点坐标';
COMMENT ON COLUMN "public"."t_region_info"."region" IS '范围坐标串MULTIPLG';
COMMENT ON COLUMN "public"."t_region_info"."organization_id" IS '监测单位ID';
COMMENT ON COLUMN "public"."t_region_info"."remark" IS '备注';
COMMENT ON COLUMN "public"."t_region_info"."create_dt" IS '记录创建时间';
COMMENT ON COLUMN "public"."t_region_info"."create_by" IS '记录创建人ID';
COMMENT ON COLUMN "public"."t_region_info"."update_dt" IS '记录更新时间';
COMMENT ON COLUMN "public"."t_region_info"."update_by" IS '记录更新人ID';
COMMENT ON COLUMN "public"."t_region_info"."status" IS '是否包含 1:包含 0:未包含';
COMMENT ON COLUMN "public"."t_region_info"."river" IS '所属河系';
COMMENT ON COLUMN "public"."t_region_info"."sample_dt" IS '采样时间 年月日即可';
COMMENT ON COLUMN "public"."t_region_info"."number" IS '编号';
COMMENT ON COLUMN "public"."t_region_info"."method" IS '采集方法';
COMMENT ON COLUMN "public"."t_region_info"."unit" IS '完成单位';
COMMENT ON COLUMN "public"."t_region_info"."administrative_code" IS '行政编码';
COMMENT ON TABLE "public"."t_region_info" IS '地物信息主表';