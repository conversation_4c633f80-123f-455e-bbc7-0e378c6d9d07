<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TRegionInfoMapper">

  <!-- 基础结果映射 -->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.TRegionInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="sheng" jdbcType="VARCHAR" property="sheng" />
    <result column="shi" jdbcType="VARCHAR" property="shi" />
    <result column="qu" jdbcType="VARCHAR" property="qu" />
    <result column="zhen" jdbcType="VARCHAR" property="zhen" />
    <result column="zuoluo" jdbcType="VARCHAR" property="zuoluo" />
    <result column="center" jdbcType="OTHER" property="center" typeHandler="com.lysjk.config.typehandler.PointTypeHandler" />
    <result column="region" jdbcType="OTHER" property="region" typeHandler="com.lysjk.config.typehandler.MultiPolygonTypeHandler" />
    <result column="organization_id" jdbcType="INTEGER" property="organizationId" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="create_dt" jdbcType="TIMESTAMP" property="createDt" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_dt" jdbcType="TIMESTAMP" property="updateDt" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="status" jdbcType="INTEGER" property="status" />
    <result column="river" jdbcType="VARCHAR" property="river" />
    <result column="sample_dt" jdbcType="TIMESTAMP" property="sampleDt" />
    <result column="number" jdbcType="VARCHAR" property="number" />
    <result column="method" jdbcType="VARCHAR" property="method" />
    <result column="unit" jdbcType="VARCHAR" property="unit" />
    <result column="administrative_code" jdbcType="VARCHAR" property="administrativeCode" />
  </resultMap>

  <!-- 几何字段专用结果映射 -->
  <resultMap id="GeometryResultMap" type="com.lysjk.entity.TRegionInfo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="center" jdbcType="OTHER" property="center" typeHandler="com.lysjk.config.typehandler.PointTypeHandler" />
    <result column="region" jdbcType="OTHER" property="region" typeHandler="com.lysjk.config.typehandler.MultiPolygonTypeHandler" />
  </resultMap>
  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    id, code, "name", sheng, shi, qu, zhen, zuoluo,
    center, region,
    organization_id, remark, create_dt, create_by, update_dt, update_by,
    status, river, sample_dt, number, method, unit, administrative_code
  </sql>

  <!-- 带几何字段文本格式的字段列表 -->
  <sql id="Base_Column_List_WithGeomText">
    id, code, "name", sheng, shi, qu, zhen, zuoluo,
    ST_AsText(center) as center_text, ST_AsText(region) as region_text,
    organization_id, remark, create_dt, create_by, update_dt, update_by,
    status, river, sample_dt, number, method, unit, administrative_code
  </sql>

  <!-- 业务字段列表（排除系统字段） -->
  <sql id="Business_Column_List">
    code, "name", sheng, shi, qu, zhen, zuoluo, center, region, organization_id, remark,
    status, river, sample_dt, number, method, unit, administrative_code
  </sql>

  <!-- 根据主键查询 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_region_info"
    where id = #{id,jdbcType=INTEGER}
  </select>

  <!-- 根据行政区划条件查询地物信息 -->
  <select id="selectByAdministrativeRegion" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_region_info"
    <where>
      <if test="sheng != null and sheng != ''">
        AND sheng = #{sheng,jdbcType=VARCHAR}
      </if>
      <if test="shi != null and shi != ''">
        AND shi = #{shi,jdbcType=VARCHAR}
      </if>
      <if test="qu != null and qu != ''">
        AND qu = #{qu,jdbcType=VARCHAR}
      </if>
      <if test="zhen != null and zhen != ''">
        AND zhen = #{zhen,jdbcType=VARCHAR}
      </if>
    </where>
    ORDER BY id
  </select>

  <!-- 根据ID查询中心点坐标 -->
  <select id="selectCenterById" parameterType="java.lang.Integer" resultType="org.locationtech.jts.geom.Point">
    select center
    from "t_region_info"
    where id = #{id,jdbcType=INTEGER}
  </select>

  <!-- 根据ID查询区域范围 -->
  <select id="selectRegionById" parameterType="java.lang.Integer" resultType="org.locationtech.jts.geom.MultiPolygon">
    select region
    from "t_region_info"
    where id = #{id,jdbcType=INTEGER}
  </select>

  <!-- 更新中心点坐标 -->
  <update id="updateCenterById">
    update "t_region_info"
    set center = #{center,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PointTypeHandler},
        update_dt = CURRENT_TIMESTAMP
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 更新区域范围 -->
  <update id="updateRegionById">
    update "t_region_info"
    set region = #{region,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.MultiPolygonTypeHandler},
        update_dt = CURRENT_TIMESTAMP
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 更新业务字段（排除系统字段） -->
  <update id="updateBusinessFields" parameterType="com.lysjk.entity.TRegionInfo">
    update "t_region_info"
    <set>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        "name" = #{name,jdbcType=VARCHAR},
      </if>
      <if test="sheng != null">
        sheng = #{sheng,jdbcType=VARCHAR},
      </if>
      <if test="shi != null">
        shi = #{shi,jdbcType=VARCHAR},
      </if>
      <if test="qu != null">
        qu = #{qu,jdbcType=VARCHAR},
      </if>
      <if test="zhen != null">
        zhen = #{zhen,jdbcType=VARCHAR},
      </if>
      <if test="zuoluo != null">
        zuoluo = #{zuoluo,jdbcType=VARCHAR},
      </if>
      <if test="center != null">
        center = #{center,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PointTypeHandler},
      </if>
      <if test="region != null">
        region = #{region,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.MultiPolygonTypeHandler},
      </if>
      <if test="organizationId != null">
        organization_id = #{organizationId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status},
      </if>
      <if test="river != null and river != ''">
        river = #{river},
      </if>
      <if test="sampleDt != null">
        sample_dt = #{sampleDt},
      </if>
      <if test="number != null and number != ''">
        number = #{number},
      </if>
      <if test="method != null and method != ''">
        method = #{method},
      </if>
      <if test="unit != null and unit != ''">
        unit = #{unit},
      </if>
      <if test="administrativeCode != null and administrativeCode != ''">
        administrative_code = #{administrativeCode},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 查询所有地物信息（用于分页） -->
  <select id="selectAll" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_region_info"
    order by id desc
  </select>

  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from "t_region_info"
    where id = #{id,jdbcType=INTEGER}
  </delete>

  <!-- 批量删除地物信息 -->
  <delete id="deleteByIds">
    delete from "t_region_info" where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.lysjk.entity.TRegionInfo" useGeneratedKeys="true">
    insert into "t_region_info" (code, "name", sheng,
      shi, qu, zhen, zuoluo,
      center, region, organization_id,
      remark, create_dt, create_by,
      update_dt, update_by, status, river,
      sample_dt, number, method, unit, administrative_code)
    values (#{code,jdbcType=VARCHAR}, #{name,jdbcType=VARCHAR}, #{sheng,jdbcType=VARCHAR},
      #{shi,jdbcType=VARCHAR}, #{qu,jdbcType=VARCHAR}, #{zhen,jdbcType=VARCHAR}, #{zuoluo,jdbcType=VARCHAR},
      #{center,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PointTypeHandler}, #{region,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.MultiPolygonTypeHandler}, #{organizationId,jdbcType=INTEGER},
      #{remark,jdbcType=VARCHAR}, #{createDt,jdbcType=TIMESTAMP}, #{createBy,jdbcType=INTEGER},
      #{updateDt,jdbcType=TIMESTAMP}, #{updateBy,jdbcType=INTEGER}, #{status,jdbcType=INTEGER}, #{river,jdbcType=VARCHAR},
      #{sampleDt,jdbcType=TIMESTAMP}, #{number,jdbcType=VARCHAR}, #{method,jdbcType=VARCHAR}, #{unit,jdbcType=VARCHAR}, #{administrativeCode,jdbcType=VARCHAR})
  </insert>

  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lysjk.entity.TRegionInfo" useGeneratedKeys="true">
    insert into "t_region_info"
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="code != null">
        code,
      </if>
      <if test="name != null">
        "name",
      </if>
      <if test="sheng != null">
        sheng,
      </if>
      <if test="shi != null">
        shi,
      </if>
      <if test="qu != null">
        qu,
      </if>
      <if test="zhen != null">
        zhen,
      </if>
      <if test="zuoluo != null">
        zuoluo,
      </if>
      <if test="center != null">
        center,
      </if>
      <if test="region != null">
        region,
      </if>
      <if test="organizationId != null">
        organization_id,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="createDt != null">
        create_dt,
      </if>
      <if test="createBy != null">
        create_by,
      </if>
      <if test="updateDt != null">
        update_dt,
      </if>
      <if test="updateBy != null">
        update_by,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="river != null and river != ''">
        river,
      </if>
      <if test="sampleDt != null">
        sample_dt,
      </if>
      <if test="number != null and number != ''">
        number,
      </if>
      <if test="method != null and method != ''">
        method,
      </if>
      <if test="unit != null and unit != ''">
        unit,
      </if>
      <if test="administrativeCode != null and administrativeCode != ''">
        administrative_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="sheng != null">
        #{sheng,jdbcType=VARCHAR},
      </if>
      <if test="shi != null">
        #{shi,jdbcType=VARCHAR},
      </if>
      <if test="qu != null">
        #{qu,jdbcType=VARCHAR},
      </if>
      <if test="zhen != null">
        #{zhen,jdbcType=VARCHAR},
      </if>
      <if test="zuoluo != null">
        #{zuoluo,jdbcType=VARCHAR},
      </if>
      <if test="center != null">
        #{center,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PointTypeHandler},
      </if>
      <if test="region != null">
        #{region,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.MultiPolygonTypeHandler},
      </if>
      <if test="organizationId != null">
        #{organizationId,jdbcType=INTEGER},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="createDt != null">
        #{createDt,jdbcType=TIMESTAMP},
      </if>
      <if test="createBy != null">
        #{createBy,jdbcType=INTEGER},
      </if>
      <if test="updateDt != null">
        #{updateDt,jdbcType=TIMESTAMP},
      </if>
      <if test="updateBy != null">
        #{updateBy,jdbcType=INTEGER},
      </if>
      <if test="status != null">
        #{status,jdbcType=INTEGER},
      </if>
      <if test="river != null and river != ''">
        #{river,jdbcType=VARCHAR},
      </if>
      <if test="sampleDt != null">
        #{sampleDt,jdbcType=TIMESTAMP},
      </if>
      <if test="number != null and number != ''">
        #{number,jdbcType=VARCHAR},
      </if>
      <if test="method != null and method != ''">
        #{method,jdbcType=VARCHAR},
      </if>
      <if test="unit != null and unit != ''">
        #{unit,jdbcType=VARCHAR},
      </if>
      <if test="administrativeCode != null and administrativeCode != ''">
        #{administrativeCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>

  <update id="updateByPrimaryKey" parameterType="com.lysjk.entity.TRegionInfo">
    update "t_region_info"
    set code = #{code,jdbcType=VARCHAR},
      "name" = #{name,jdbcType=VARCHAR},
      sheng = #{sheng,jdbcType=VARCHAR},
      shi = #{shi,jdbcType=VARCHAR},
      qu = #{qu,jdbcType=VARCHAR},
      zhen = #{zhen,jdbcType=VARCHAR},
      zuoluo = #{zuoluo,jdbcType=VARCHAR},
      center = #{center,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PointTypeHandler},
      region = #{region,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.MultiPolygonTypeHandler},
      organization_id = #{organizationId,jdbcType=INTEGER},
      remark = #{remark,jdbcType=VARCHAR},
      create_dt = #{createDt,jdbcType=TIMESTAMP},
      create_by = #{createBy,jdbcType=INTEGER},
      update_dt = #{updateDt,jdbcType=TIMESTAMP},
      update_by = #{updateBy,jdbcType=INTEGER},
      status = #{status,jdbcType=INTEGER},
      river = #{river,jdbcType=VARCHAR},
      sample_dt = #{sampleDt,jdbcType=TIMESTAMP},
      number = #{number,jdbcType=VARCHAR},
      method = #{method,jdbcType=VARCHAR},
      unit = #{unit,jdbcType=VARCHAR},
      administrative_code = #{administrativeCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 地物信息与监测点关联查询结果映射 -->
  <resultMap id="RegionInfoWithMonitoringPointsResultMap" type="com.lysjk.dto.TRegionInfoWithMonitoringPointsDTO">
    <!-- 地物信息 -->
    <association property="regionInfo" javaType="com.lysjk.entity.TRegionInfo">
      <id column="r_id" jdbcType="INTEGER" property="id" />
      <result column="r_code" jdbcType="VARCHAR" property="code" />
      <result column="r_name" jdbcType="VARCHAR" property="name" />
      <result column="r_sheng" jdbcType="VARCHAR" property="sheng" />
      <result column="r_shi" jdbcType="VARCHAR" property="shi" />
      <result column="r_qu" jdbcType="VARCHAR" property="qu" />
      <result column="r_zhen" jdbcType="VARCHAR" property="zhen" />
      <result column="r_zuoluo" jdbcType="VARCHAR" property="zuoluo" />
      <result column="r_center" jdbcType="OTHER" property="center" typeHandler="com.lysjk.config.typehandler.PointTypeHandler" />
      <result column="r_region" jdbcType="OTHER" property="region" typeHandler="com.lysjk.config.typehandler.MultiPolygonTypeHandler" />
      <result column="r_organization_id" jdbcType="INTEGER" property="organizationId" />
      <result column="r_remark" jdbcType="VARCHAR" property="remark" />
      <result column="r_create_dt" jdbcType="TIMESTAMP" property="createDt" />
      <result column="r_create_by" jdbcType="INTEGER" property="createBy" />
      <result column="r_update_dt" jdbcType="TIMESTAMP" property="updateDt" />
      <result column="r_update_by" jdbcType="INTEGER" property="updateBy" />
    </association>
    <!-- 监测点列表 -->
    <collection property="monitoringPoints" ofType="com.lysjk.entity.TMonitoringPoint">
      <id column="mp_id" jdbcType="INTEGER" property="id" />
      <result column="mp_region_id" jdbcType="INTEGER" property="regionId" />
      <result column="mp_name" jdbcType="VARCHAR" property="name" />
      <result column="mp_location" jdbcType="OTHER" property="location" typeHandler="com.lysjk.config.typehandler.PointTypeHandler" />
      <result column="mp_code" jdbcType="VARCHAR" property="code" />
      <result column="mp_create_dt" jdbcType="TIMESTAMP" property="createDt" />
      <result column="mp_create_by" jdbcType="INTEGER" property="createBy" />
      <result column="mp_update_dt" jdbcType="TIMESTAMP" property="updateDt" />
      <result column="mp_update_by" jdbcType="INTEGER" property="updateBy" />
      <result column="mp_remark" jdbcType="VARCHAR" property="remark" />
    </collection>
  </resultMap>

  <!-- 查询地物信息及其关联的监测点列表 -->
  <select id="selectRegionInfoWithMonitoringPoints" resultMap="RegionInfoWithMonitoringPointsResultMap">
    SELECT
      r.id as r_id,
      r.code as r_code,
      r.name as r_name,
      r.sheng as r_sheng,
      r.shi as r_shi,
      r.qu as r_qu,
      r.zhen as r_zhen,
      r.zuoluo as r_zuoluo,
      r.center as r_center,
      r.region as r_region,
      r.organization_id as r_organization_id,
      r.remark as r_remark,
      r.create_dt as r_create_dt,
      r.create_by as r_create_by,
      r.update_dt as r_update_dt,
      r.update_by as r_update_by,
      mp.id as mp_id,
      mp.region_id as mp_region_id,
      mp.name as mp_name,
      mp.location as mp_location,
      mp.code as mp_code,
      mp.create_dt as mp_create_dt,
      mp.create_by as mp_create_by,
      mp.update_dt as mp_update_dt,
      mp.update_by as mp_update_by,
      mp.remark as mp_remark
    FROM t_region_info r
    LEFT JOIN t_monitoring_point mp ON r.id = mp.region_id
    ORDER BY r.id DESC, mp.id ASC
  </select>

  <!-- 根据地物ID查询地物信息及其关联的监测点列表 -->
  <select id="selectRegionInfoWithMonitoringPointsById" parameterType="java.lang.Integer" resultMap="RegionInfoWithMonitoringPointsResultMap">
    SELECT
      r.id as r_id,
      r.code as r_code,
      r.name as r_name,
      r.sheng as r_sheng,
      r.shi as r_shi,
      r.qu as r_qu,
      r.zhen as r_zhen,
      r.zuoluo as r_zuoluo,
      r.center as r_center,
      r.region as r_region,
      r.organization_id as r_organization_id,
      r.remark as r_remark,
      r.create_dt as r_create_dt,
      r.create_by as r_create_by,
      r.update_dt as r_update_dt,
      r.update_by as r_update_by,
      mp.id as mp_id,
      mp.region_id as mp_region_id,
      mp.name as mp_name,
      mp.location as mp_location,
      mp.code as mp_code,
      mp.create_dt as mp_create_dt,
      mp.create_by as mp_create_by,
      mp.update_dt as mp_update_dt,
      mp.update_by as mp_update_by,
      mp.remark as mp_remark
    FROM t_region_info r
    LEFT JOIN t_monitoring_point mp ON r.id = mp.region_id
    WHERE r.id = #{regionId,jdbcType=INTEGER}
    ORDER BY mp.id ASC
  </select>

  <!-- 分页查询地物信息及其关联的监测点列表 -->
  <select id="selectRegionInfoWithMonitoringPointsForPage" resultMap="RegionInfoWithMonitoringPointsResultMap">
    SELECT
      r.id as r_id,
      r.code as r_code,
      r.name as r_name,
      r.sheng as r_sheng,
      r.shi as r_shi,
      r.qu as r_qu,
      r.zhen as r_zhen,
      r.zuoluo as r_zuoluo,
      r.center as r_center,
      r.region as r_region,
      r.organization_id as r_organization_id,
      r.remark as r_remark,
      r.create_dt as r_create_dt,
      r.create_by as r_create_by,
      r.update_dt as r_update_dt,
      r.update_by as r_update_by,
      mp.id as mp_id,
      mp.region_id as mp_region_id,
      mp.name as mp_name,
      mp.location as mp_location,
      mp.code as mp_code,
      mp.create_dt as mp_create_dt,
      mp.create_by as mp_create_by,
      mp.update_dt as mp_update_dt,
      mp.update_by as mp_update_by,
      mp.remark as mp_remark
    FROM t_region_info r
    LEFT JOIN t_monitoring_point mp ON r.id = mp.region_id
    ORDER BY r.id DESC, mp.id ASC
  </select>

  <!-- 根据地物编码查询地物信息 -->
  <select id="selectByCode" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_region_info"
    where code = #{code,jdbcType=VARCHAR}::varchar
    LIMIT 1
  </select>

  <!-- 分页条件查询地物信息 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from "t_region_info"
    <where>
      <if test="code != null and code != ''">
        and code like concat('%', #{code}, '%')
      </if>
      <if test="name != null and name != ''">
        and "name" like concat('%', #{name}, '%')
      </if>
      <if test="sheng != null and sheng != ''">
        and sheng like concat('%', #{sheng}, '%')
      </if>
      <if test="shi != null and shi != ''">
        and shi like concat('%', #{shi}, '%')
      </if>
      <if test="qu != null and qu != ''">
        and qu like concat('%', #{qu}, '%')
      </if>
      <if test="zhen != null and zhen != ''">
        and zhen like concat('%', #{zhen}, '%')
      </if>
      <if test="zuoluo != null and zuoluo != ''">
        and zuoluo like concat('%', #{zuoluo}, '%')
      </if>
      <if test="organizationId != null">
        and organization_id = #{organizationId}
      </if>
      <if test="status != null">
        and status = #{status}
      </if>
      <if test="river != null and river != ''">
        and river like concat('%', #{river}, '%')
      </if>
      <if test="number != null and number != ''">
        and number like concat('%', #{number}, '%')
      </if>
      <if test="method != null and method != ''">
        and method like concat('%', #{method}, '%')
      </if>
      <if test="unit != null and unit != ''">
        and unit like concat('%', #{unit}, '%')
      </if>
      <if test="administrativeCode != null and administrativeCode != ''">
        and administrative_code like concat('%', #{administrativeCode}, '%')
      </if>
      <if test="sampleDt != null">
        and sample_dt = #{sampleDt}
      </if>
    </where>
    order by create_dt desc
  </select>

</mapper>