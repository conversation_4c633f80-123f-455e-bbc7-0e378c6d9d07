<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TWaterEnvironmentMapper">

  <!-- 基础结果映射 -->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.TWaterEnvironment">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="point_id" jdbcType="INTEGER" property="pointId" />
    <result column="region_id" jdbcType="INTEGER" property="regionId" />
    <result column="monitoring_dt" jdbcType="TIMESTAMP" property="monitoringDt" />
    <result column="wind_speed" jdbcType="REAL" property="windSpeed" />
    <result column="wind_direction" jdbcType="VARCHAR" property="windDirection" />
    <result column="air_temperature" jdbcType="REAL" property="airTemperature" />
    <result column="water_temperature" jdbcType="REAL" property="waterTemperature" />
    <result column="water_transparency" jdbcType="REAL" property="waterTransparency" />
    <result column="extension" jdbcType="OTHER" property="extension" />
    <result column="water_depth" jdbcType="REAL" property="waterDepth" />
    <result column="weather" jdbcType="VARCHAR" property="weather" />
    <result column="surface_condition" jdbcType="VARCHAR" property="surfaceCondition" />
    <result column="photo_folder" jdbcType="VARCHAR" property="photoFolder" />
    <result column="water_turbidity" jdbcType="REAL" property="waterTurbidity" />
    <result column="dissolved_oxyg" jdbcType="REAL" property="dissolvedOxyg" />
    <result column="water_turbidity3" jdbcType="VARCHAR" property="waterTurbidity3" />
    <result column="elevation" jdbcType="REAL" property="elevation" />
    <result column="create_dt" jdbcType="TIMESTAMP" property="createDt" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_dt" jdbcType="TIMESTAMP" property="updateDt" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ph" jdbcType="REAL" property="ph" />
    <result column="reflectance_spectrum" jdbcType="VARCHAR" property="reflectanceSpectrum" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    id, point_id, region_id, monitoring_dt, wind_speed, wind_direction, air_temperature,
    water_temperature, water_transparency, extension::text as extension, water_depth, weather, surface_condition,
    photo_folder, water_turbidity, dissolved_oxyg, water_turbidity3, elevation,
    create_dt, create_by, update_dt, update_by, remark, ph, reflectance_spectrum
  </sql>

  <!-- 新增水域环境信息 -->
  <insert id="insert" useGeneratedKeys="true" keyProperty="id">
    insert into t_water_environment
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pointId != null">point_id,</if>
      <if test="regionId != null">region_id,</if>
      <if test="monitoringDt != null">monitoring_dt,</if>
      <if test="windSpeed != null">wind_speed,</if>
      <if test="windDirection != null and windDirection != ''">wind_direction,</if>
      <if test="airTemperature != null">air_temperature,</if>
      <if test="waterTemperature != null">water_temperature,</if>
      <if test="waterTransparency != null">water_transparency,</if>
      <if test="extension != null">extension,</if>
      <if test="waterDepth != null">water_depth,</if>
      <if test="weather != null and weather != ''">weather,</if>
      <if test="surfaceCondition != null and surfaceCondition != ''">surface_condition,</if>
      <if test="photoFolder != null and photoFolder != ''">photo_folder,</if>
      <if test="waterTurbidity != null">water_turbidity,</if>
      <if test="dissolvedOxyg != null">dissolved_oxyg,</if>
      <if test="waterTurbidity3 != null and waterTurbidity3 != ''">water_turbidity3,</if>
      <if test="elevation != null">elevation,</if>
      <if test="createDt != null">create_dt,</if>
      <if test="createBy != null">create_by,</if>
      <if test="updateDt != null">update_dt,</if>
      <if test="updateBy != null">update_by,</if>
      <if test="remark != null and remark != ''">remark,</if>
      <if test="ph != null">ph,</if>
      <if test="reflectanceSpectrum != null and reflectanceSpectrum != ''">reflectance_spectrum,</if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pointId != null">#{pointId},</if>
      <if test="regionId != null">#{regionId},</if>
      <if test="monitoringDt != null">#{monitoringDt},</if>
      <if test="windSpeed != null">#{windSpeed},</if>
      <if test="windDirection != null and windDirection != ''">#{windDirection},</if>
      <if test="airTemperature != null">#{airTemperature},</if>
      <if test="waterTemperature != null">#{waterTemperature},</if>
      <if test="waterTransparency != null">#{waterTransparency},</if>
      <if test="extension != null and extension != ''">#{extension}::json,</if>
      <if test="waterDepth != null">#{waterDepth},</if>
      <if test="weather != null and weather != ''">#{weather},</if>
      <if test="surfaceCondition != null and surfaceCondition != ''">#{surfaceCondition},</if>
      <if test="photoFolder != null and photoFolder != ''">#{photoFolder},</if>
      <if test="waterTurbidity != null">#{waterTurbidity},</if>
      <if test="dissolvedOxyg != null">#{dissolvedOxyg},</if>
      <if test="waterTurbidity3 != null and waterTurbidity3 != ''">#{waterTurbidity3},</if>
      <if test="elevation != null">#{elevation},</if>
      <if test="createDt != null">#{createDt},</if>
      <if test="createBy != null">#{createBy},</if>
      <if test="updateDt != null">#{updateDt},</if>
      <if test="updateBy != null">#{updateBy},</if>
      <if test="remark != null and remark != ''">#{remark},</if>
      <if test="ph != null">#{ph},</if>
      <if test="reflectanceSpectrum != null and reflectanceSpectrum != ''">#{reflectanceSpectrum},</if>
    </trim>
  </insert>

  <!-- 批量删除水域环境信息 -->
  <delete id="deleteByIds">
    delete from t_water_environment where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>

  <!-- 更新水域环境信息 -->
  <update id="update">
    update t_water_environment
    <set>
      <if test="pointId != null">point_id = #{pointId},</if>
      <if test="regionId != null">region_id = #{regionId},</if>
      <if test="monitoringDt != null">monitoring_dt = #{monitoringDt},</if>
      <if test="windSpeed != null">wind_speed = #{windSpeed},</if>
      <if test="windDirection != null and windDirection != ''">wind_direction = #{windDirection},</if>
      <if test="airTemperature != null">air_temperature = #{airTemperature},</if>
      <if test="waterTemperature != null">water_temperature = #{waterTemperature},</if>
      <if test="waterTransparency != null">water_transparency = #{waterTransparency},</if>
      <if test="extension != null and extension != ''">extension = #{extension}::json,</if>
      <if test="waterDepth != null">water_depth = #{waterDepth},</if>
      <if test="weather != null and weather != ''">weather = #{weather},</if>
      <if test="surfaceCondition != null and surfaceCondition != ''">surface_condition = #{surfaceCondition},</if>
      <if test="photoFolder != null and photoFolder != ''">photo_folder = #{photoFolder},</if>
      <if test="waterTurbidity != null">water_turbidity = #{waterTurbidity},</if>
      <if test="dissolvedOxyg != null">dissolved_oxyg = #{dissolvedOxyg},</if>
      <if test="waterTurbidity3 != null and waterTurbidity3 != ''">water_turbidity3 = #{waterTurbidity3},</if>
      <if test="elevation != null">elevation = #{elevation},</if>
      <if test="updateDt != null">update_dt = #{updateDt},</if>
      <if test="updateBy != null">update_by = #{updateBy},</if>
      <if test="remark != null and remark != ''">remark = #{remark},</if>
      <if test="ph != null">ph = #{ph},</if>
      <if test="reflectanceSpectrum != null and reflectanceSpectrum != ''">reflectance_spectrum = #{reflectanceSpectrum},</if>
    </set>
    where id = #{id}
  </update>

  <!-- 分页查询水域环境信息 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from t_water_environment
    <where>
      <if test="pointId != null">
        and point_id = #{pointId}
      </if>
      <if test="regionId != null">
        and region_id = #{regionId}
      </if>
      <if test="windSpeed != null">
        and wind_speed = #{windSpeed}
      </if>
      <if test="windDirection != null and windDirection != ''">
        and wind_direction like concat('%', #{windDirection}, '%')
      </if>
      <if test="airTemperature != null">
        and air_temperature = #{airTemperature}
      </if>
      <if test="waterTemperature != null">
        and water_temperature = #{waterTemperature}
      </if>
      <if test="waterTransparency != null">
        and water_transparency = #{waterTransparency}
      </if>
      <if test="waterDepth != null">
        and water_depth = #{waterDepth}
      </if>
      <if test="weather != null and weather != ''">
        and weather like concat('%', #{weather}, '%')
      </if>
      <if test="surfaceCondition != null and surfaceCondition != ''">
        and surface_condition like concat('%', #{surfaceCondition}, '%')
      </if>
      <if test="waterTurbidity != null">
        and water_turbidity = #{waterTurbidity}
      </if>
      <if test="dissolvedOxyg != null">
        and dissolved_oxyg = #{dissolvedOxyg}
      </if>
      <if test="elevation != null">
        and elevation = #{elevation}
      </if>
      <if test="ph != null">
        and ph = #{ph}
      </if>
      <if test="reflectanceSpectrum != null and reflectanceSpectrum != ''">
        and reflectance_spectrum like concat('%', #{reflectanceSpectrum}, '%')
      </if>
      <if test="extension != null and extension != ''">
        and extension::text like concat('%', #{extension}, '%')
      </if>
      <if test="waterTurbidity3 != null and waterTurbidity3 != ''">
        and water_turbidity3 like concat('%', #{waterTurbidity3}, '%')
      </if>
    </where>
    order by monitoring_dt desc, create_dt desc
  </select>

</mapper>
