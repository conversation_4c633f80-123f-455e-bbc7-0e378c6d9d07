<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.lysjk.mapper.TMonitoringPointMapper">

  <!-- 基础结果映射 -->
  <resultMap id="BaseResultMap" type="com.lysjk.entity.TMonitoringPoint">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="region_id" jdbcType="INTEGER" property="regionId" />
    <result column="name" jdbcType="VARCHAR" property="name" />
    <result column="location" jdbcType="OTHER" property="location" typeHandler="com.lysjk.config.typehandler.PointTypeHandler" />
    <result column="code" jdbcType="VARCHAR" property="code" />
    <result column="create_dt" jdbcType="TIMESTAMP" property="createDt" />
    <result column="create_by" jdbcType="INTEGER" property="createBy" />
    <result column="update_dt" jdbcType="TIMESTAMP" property="updateDt" />
    <result column="update_by" jdbcType="INTEGER" property="updateBy" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
  </resultMap>

  <!-- 基础字段列表 -->
  <sql id="Base_Column_List">
    id, region_id, name, location, code, create_dt, create_by, update_dt, update_by, remark
  </sql>

  <!-- 业务字段列表（排除系统字段） -->
  <sql id="Business_Column_List">
    region_id, name, location, code, remark
  </sql>

  <!-- 根据主键查询监测点 -->
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM t_monitoring_point
    WHERE id = #{id,jdbcType=INTEGER}
  </select>

  <!-- 根据主键删除监测点 -->
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    DELETE FROM t_monitoring_point
    WHERE id = #{id,jdbcType=INTEGER}
  </delete>

  <!-- 批量删除监测点信息 -->
  <delete id="deleteByIds">
    delete from t_monitoring_point where id in
    <foreach collection="ids" item="id" open="(" separator="," close=")">
      #{id}
    </foreach>
  </delete>
  <!-- 新增监测点信息 -->
  <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.lysjk.entity.TMonitoringPoint" useGeneratedKeys="true">
    INSERT INTO t_monitoring_point
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        region_id,
      </if>
      <if test="name != null">
        name,
      </if>
      <if test="location != null">
        location,
      </if>
      <if test="code != null">
        code,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <!-- 系统字段自动设置 -->
      create_by,
      update_by,
      create_dt,
      update_dt
    </trim>
    <trim prefix="VALUES (" suffix=")" suffixOverrides=",">
      <if test="regionId != null">
        #{regionId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        #{name,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        #{location,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PointTypeHandler},
      </if>
      <if test="code != null">
        #{code,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <!-- 系统字段自动设置 -->
      #{createBy},
      #{updateBy},
      NOW(),
      NOW()
    </trim>
  </insert>
  <!-- 更新监测点业务字段（排除系统字段） -->
  <update id="updateBusinessFields" parameterType="com.lysjk.entity.TMonitoringPoint">
    UPDATE t_monitoring_point
    <set>
      <if test="regionId != null">
        region_id = #{regionId,jdbcType=INTEGER},
      </if>
      <if test="name != null">
        name = #{name,jdbcType=VARCHAR},
      </if>
      <if test="location != null">
        location = #{location,jdbcType=OTHER,typeHandler=com.lysjk.config.typehandler.PointTypeHandler},
      </if>
      <if test="code != null">
        code = #{code,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <!-- 系统字段自动更新 -->
      update_dt = NOW()
    </set>
    WHERE id = #{id,jdbcType=INTEGER}
  </update>

  <!-- 分页条件查询监测点信息 -->
  <select id="pageQuery" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM t_monitoring_point
    <where>
      <if test="regionId != null">
        AND region_id = #{regionId,jdbcType=INTEGER}
      </if>
      <if test="name != null and name != ''">
        AND name::varchar LIKE CONCAT('%', #{name,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="code != null and code != ''">
        AND code::varchar LIKE CONCAT('%', #{code,jdbcType=VARCHAR}::varchar, '%')
      </if>
      <if test="remark != null and remark != ''">
        AND remark::varchar LIKE CONCAT('%', #{remark,jdbcType=VARCHAR}::varchar, '%')
      </if>
    </where>
    ORDER BY create_dt DESC
  </select>

  <!-- 根据监测点编码查询监测点信息（排除指定ID） -->
  <select id="selectByCodeExcludeId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM t_monitoring_point
    WHERE code = #{code,jdbcType=VARCHAR}::varchar
    <if test="excludeId != null">
      AND id != #{excludeId,jdbcType=INTEGER}
    </if>
    LIMIT 1
  </select>

  <!-- 根据地物ID查询监测点 -->
  <select id="selectByRegionId" resultMap="BaseResultMap">
    SELECT
    <include refid="Base_Column_List" />
    FROM t_monitoring_point
    WHERE region_id = #{regionId,jdbcType=INTEGER}
    ORDER BY id DESC
  </select>

</mapper>