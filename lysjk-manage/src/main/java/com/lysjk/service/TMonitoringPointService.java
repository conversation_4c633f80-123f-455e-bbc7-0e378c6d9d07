package com.lysjk.service;

import com.lysjk.dto.TMonitoringPointPageQueryDTO;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.result.PageResult;

import java.util.List;

/**
 * 监测点信息服务接口
 */
public interface TMonitoringPointService {

    /**
     * 新增监测点信息
     * @param monitoringPoint 监测点信息
     */
    void save(TMonitoringPoint monitoringPoint);

    /**
     * 批量删除监测点信息
     * @param ids ID列表
     */
    void deleteBatch(List<Integer> ids);

    /**
     * 更新监测点信息
     * @param monitoringPoint 监测点信息
     */
    void update(TMonitoringPoint monitoringPoint);

    /**
     * 根据主键查询监测点信息
     * @param id 监测点ID
     * @return 监测点信息
     */
    TMonitoringPoint selectByPrimaryKey(Integer id);

    /**
     * 分页条件查询监测点信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    PageResult selectPage(TMonitoringPointPageQueryDTO pageQueryDTO);

    /**
     * 根据地物ID查询监测点
     * @param regionId 地物ID
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByRegionId(Integer regionId);

    /**
     * 检查监测点编码唯一性（简化版本）
     * @param code 监测点编码
     */
    void checkCodeUnique(String code);
}
