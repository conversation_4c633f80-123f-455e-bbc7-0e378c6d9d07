package com.lysjk.config.typehandler;

import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * JSON字符串类型处理器
 * 处理Java String对象与PostgreSQL json/jsonb类型之间的转换
 * 用于存储JSON格式的字符串数据
 * 注意：继承BaseTypeHandler<Object>而不是BaseTypeHandler<String>，避免被MyBatis自动推断为String类型的默认处理器
 */
@Slf4j
// @MappedTypes(String.class) // 注释掉，避免自动注册为所有String的默认处理器
public class JsonStringTypeHandler extends BaseTypeHandler<Object> {

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, Object parameter, JdbcType jdbcType) throws SQLException {
        try {
            // 将Object转换为String，然后转换为PostgreSQL JSON格式
            String stringParameter = parameter != null ? parameter.toString() : null;
            PGobject pGobject = new PGobject();
            pGobject.setType("json");

            if (stringParameter != null && !stringParameter.trim().isEmpty()) {
                pGobject.setValue(stringParameter);

                if (log.isDebugEnabled()) {
                    log.debug("设置JSON字符串参数: {}", stringParameter);
                }
            } else {
                pGobject.setValue(null);
            }

            ps.setObject(i, pGobject);

        } catch (Exception e) {
            log.error("设置JSON字符串参数失败：{}", parameter, e);
            throw new SQLException("设置JSON字符串参数失败，数据：" + parameter, e);
        }
    }

    @Override
    public Object getNullableResult(ResultSet rs, String columnName) throws SQLException {
        return parseJsonString(rs.getObject(columnName));
    }

    @Override
    public Object getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        return parseJsonString(rs.getObject(columnIndex));
    }

    @Override
    public Object getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        return parseJsonString(cs.getObject(columnIndex));
    }

    /**
     * 解析JSON数据为字符串
     */
    private String parseJsonString(Object object) throws SQLException {
        if (object == null) {
            return null;
        }

        try {
            String jsonString;
            if (object instanceof PGobject) {
                jsonString = ((PGobject) object).getValue();
            } else if (object instanceof String) {
                jsonString = (String) object;
            } else {
                log.warn("未知的JSON数据类型: {}", object.getClass().getName());
                return null;
            }

            if (log.isDebugEnabled()) {
                log.debug("解析JSON字符串成功: {}", jsonString);
            }
            
            return jsonString;

        } catch (Exception e) {
            log.error("解析JSON字符串失败: {}", object, e);
            throw new SQLException("无法解析JSON字符串数据", e);
        }
    }
}
