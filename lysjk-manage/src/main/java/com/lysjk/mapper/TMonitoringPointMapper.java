package com.lysjk.mapper;

import com.github.pagehelper.Page;
import com.lysjk.anno.AutoFill;
import com.lysjk.dto.TMonitoringPointPageQueryDTO;
import com.lysjk.entity.TMonitoringPoint;
import com.lysjk.enums.Operation;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 监测点信息表数据访问层
 */
@Mapper
public interface TMonitoringPointMapper {

    /**
     * 新增监测点信息
     * @param monitoringPoint 监测点信息
     * @return 影响行数
     */
    @AutoFill(Operation.INSERT)
    int insertSelective(TMonitoringPoint monitoringPoint);

    /**
     * 批量删除监测点信息
     * @param ids ID列表
     * @return 影响行数
     */
    int deleteByIds(List<Integer> ids);

    /**
     * 更新监测点业务字段
     * @param monitoringPoint 监测点信息
     * @return 影响行数
     */
    @AutoFill(Operation.UPDATE)
    int updateBusinessFields(TMonitoringPoint monitoringPoint);

    /**
     * 根据主键查询监测点
     * @param id 监测点ID
     * @return 监测点信息
     */
    TMonitoringPoint selectByPrimaryKey(Integer id);

    /**
     * 分页条件查询监测点信息
     * @param pageQueryDTO 分页查询条件
     * @return 分页结果
     */
    Page<TMonitoringPoint> pageQuery(TMonitoringPointPageQueryDTO pageQueryDTO);

    /**
     * 根据地物ID查询监测点
     * @param regionId 地物ID
     * @return 监测点列表
     */
    List<TMonitoringPoint> selectByRegionId(@Param("regionId") Integer regionId);

    /**
     * 根据监测点编码查询监测点信息
     * 用于简化的编码唯一性检查
     * @param code 监测点编码
     * @return 监测点信息，不存在返回null
     */
    TMonitoringPoint selectByCode(@Param("code") String code);
}