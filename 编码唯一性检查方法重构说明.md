# 编码唯一性检查方法重构说明

## 重构目标

将TRegionInfoServiceImpl和TMonitoringPointServiceImpl中的isCodeUnique方法改为仿照TOrganizationInfoServiceImpl中的checkNameUnique写法，简化业务逻辑，去掉excludeId参数，只保留String code参数。

## 重构内容

### 1. TRegionInfoServiceImpl 重构

#### 1.1 Mapper接口修改
- **文件**: `TRegionInfoMapper.java`
- **新增方法**: `TRegionInfo selectByCode(@Param("code") String code)`
- **用途**: 用于简化的编码唯一性检查

#### 1.2 Mapper XML修改
- **文件**: `TRegionInfoMapper.xml`
- **新增查询**: 
```xml
<!-- 根据地物编码查询地物信息 -->
<select id="selectByCode" resultMap="BaseResultMap">
  select
  <include refid="Base_Column_List" />
  from "t_region_info"
  where code = #{code,jdbcType=VARCHAR}::varchar
  LIMIT 1
</select>
```

#### 1.3 Service接口修改
- **文件**: `TRegionInfoService.java`
- **新增方法**: `void checkCodeUnique(String code)`
- **保留原方法**: `boolean isCodeUnique(String code, Integer excludeId)` (向后兼容)

#### 1.4 Service实现类修改
- **文件**: `TRegionInfoServiceImpl.java`
- **新增方法**: `checkCodeUnique(String code)` - 仿照TOrganizationInfoServiceImpl的checkNameUnique模式
- **修改新增业务逻辑**: 将`isCodeUnique(regionInfo.getCode(), null)`改为`checkCodeUnique(regionInfo.getCode())`
- **修改更新业务逻辑**: 将`isCodeUnique(regionInfo.getCode(), regionInfo.getId())`改为`checkCodeUnique(regionInfo.getCode())`

### 2. TMonitoringPointServiceImpl 重构

#### 2.1 Mapper接口修改
- **文件**: `TMonitoringPointMapper.java`
- **新增方法**: `TMonitoringPoint selectByCode(@Param("code") String code)`
- **用途**: 用于简化的编码唯一性检查

#### 2.2 Mapper XML修改
- **文件**: `TMonitoringPointMapper.xml`
- **新增查询**:
```xml
<!-- 根据监测点编码查询监测点信息 -->
<select id="selectByCode" resultMap="BaseResultMap">
  SELECT
  <include refid="Base_Column_List" />
  FROM t_monitoring_point
  WHERE code = #{code,jdbcType=VARCHAR}::varchar
  LIMIT 1
</select>
```

#### 2.3 Service接口修改
- **文件**: `TMonitoringPointService.java`
- **新增方法**: `void checkCodeUnique(String code)`

#### 2.4 Service实现类修改
- **文件**: `TMonitoringPointServiceImpl.java`
- **新增方法**: `checkCodeUnique(String code)` - 仿照TOrganizationInfoServiceImpl的checkNameUnique模式
- **修改新增业务逻辑**: 将`isCodeUnique(monitoringPoint.getCode(), null)`改为`checkCodeUnique(monitoringPoint.getCode())`
- **修改更新业务逻辑**: 将`isCodeUnique(monitoringPoint.getCode(), monitoringPoint.getId())`改为`checkCodeUnique(monitoringPoint.getCode())`
- **保留原方法**: `isCodeUnique(String code, Integer excludeId)` (向后兼容)

## 重构后的业务逻辑对比

### 重构前 (复杂版本)
```java
// 新增时
boolean isUnique = isCodeUnique(regionInfo.getCode(), null);
if (!isUnique) {
    throw new ValidationException.DuplicateDataException("地物编码", regionInfo.getCode());
}

// 更新时
boolean isUnique = isCodeUnique(regionInfo.getCode(), regionInfo.getId());
if (!isUnique) {
    throw new ValidationException.DuplicateDataException("地物编码", regionInfo.getCode());
}
```

### 重构后 (简化版本)
```java
// 新增和更新都使用相同逻辑
checkCodeUnique(regionInfo.getCode());
```

## checkCodeUnique方法实现

### TRegionInfoServiceImpl
```java
@Override
public void checkCodeUnique(String code) {
    if (code == null || code.trim().isEmpty()) {
        throw new IllegalArgumentException("地物编码不能为空");
    }

    log.info("检查地物编码唯一性 - 编码:{}", code);

    TRegionInfo existing = tRegionInfoMapper.selectByCode(code.trim());
    if (existing != null) {
        log.warn("地物编码已存在 - 编码:{}, 已存在ID:{}", code, existing.getId());
        throw new ValidationException.DuplicateDataException("地物编码", code);
    }

    log.info("地物编码唯一性检查通过 - 编码:{}", code);
}
```

### TMonitoringPointServiceImpl
```java
@Override
public void checkCodeUnique(String code) {
    if (code == null || code.trim().isEmpty()) {
        throw new IllegalArgumentException("监测点编码不能为空");
    }

    log.info("检查监测点编码唯一性 - 编码:{}", code);

    TMonitoringPoint existing = tMonitoringPointMapper.selectByCode(code.trim());
    if (existing != null) {
        log.warn("监测点编码已存在 - 编码:{}, 已存在ID:{}", code, existing.getId());
        throw new ValidationException.DuplicateDataException("监测点编码", code);
    }

    log.info("监测点编码唯一性检查通过 - 编码:{}", code);
}
```

## 重构优势

1. **代码简化**: 新增和更新业务逻辑统一，减少重复代码
2. **逻辑清晰**: 直接抛出异常，不需要返回值判断
3. **一致性**: 与TOrganizationInfoServiceImpl的checkNameUnique保持一致的编码风格
4. **向后兼容**: 保留原有的isCodeUnique方法，不影响其他可能的调用
5. **类型安全**: 使用明确的类型转换`::varchar`避免PostgreSQL类型推断错误

## 注意事项

1. **向后兼容**: 原有的isCodeUnique方法仍然保留，确保不影响其他可能的调用
2. **异常处理**: 新方法直接抛出异常，调用方不需要额外的异常处理
3. **日志记录**: 保持详细的日志记录，便于问题排查
4. **参数验证**: 对空值和空字符串进行严格验证
5. **数据库查询**: 使用新的selectByCode方法，避免复杂的排除逻辑

## 测试建议

1. **新增功能测试**: 验证新增时编码重复检查是否正常工作
2. **更新功能测试**: 验证更新时编码重复检查是否正常工作
3. **边界条件测试**: 测试空值、空字符串等边界情况
4. **异常处理测试**: 验证异常抛出和处理是否正确
5. **向后兼容测试**: 确保原有的isCodeUnique方法仍然可用
