CREATE TABLE "public"."t_monitoring_point" (
"id" int4 NOT NULL GENERATED ALWAYS AS IDENTITY (
    INCREMENT 1
    MINVALUE  1
    MAXVALUE 2147483647
    START 1
    CACHE 1
),
"region_id" int4,
"name" varchar(255) COLLATE "pg_catalog"."default",
"location" geometry(POINT),
"code" varchar(255) COLLATE "pg_catalog"."default",
"create_dt" timestamp(0),
"create_by" int4,
"update_dt" timestamp(0),
"update_by" int4,
"remark" varchar(255) COLLATE "pg_catalog"."default",
CONSTRAINT "t_monitoring_point_pkey" PRIMARY KEY ("id")
);
ALTER TABLE "public"."t_monitoring_point"
    OWNER TO "postgres";
COMMENT ON COLUMN "public"."t_monitoring_point"."id" IS '唯一ID';
COMMENT ON COLUMN "public"."t_monitoring_point"."region_id" IS '所属地物ID';
COMMENT ON COLUMN "public"."t_monitoring_point"."name" IS '监测点名称';
COMMENT ON COLUMN "public"."t_monitoring_point"."location" IS '地理坐标';
COMMENT ON COLUMN "public"."t_monitoring_point"."code" IS '监测点编号';
COMMENT ON COLUMN "public"."t_monitoring_point"."create_dt" IS '记录创建时间';
COMMENT ON COLUMN "public"."t_monitoring_point"."create_by" IS '记录创建者ID';
COMMENT ON COLUMN "public"."t_monitoring_point"."update_dt" IS '记录修改时间';
COMMENT ON COLUMN "public"."t_monitoring_point"."update_by" IS '记录修改者ID';
COMMENT ON COLUMN "public"."t_monitoring_point"."remark" IS '备注';
COMMENT ON TABLE "public"."t_monitoring_point" IS '监测点信息表';